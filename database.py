"""
Database configuration and models for the chatbot backend
"""
import os
import asyncio
from datetime import datetime
from typing import Optional, List
from sqlalchemy import <PERSON>umn, Integer, String, Text, DateTime, <PERSON><PERSON>an, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import relationship, sessionmaker
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT")
DB_USER = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")
DB_NAME = os.getenv("DB_NAME")
DB_POOL_SIZE = int(os.getenv("DB_POOL_SIZE", 20))

# Create database URLs
DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
SYNC_DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Create async engine
async_engine = create_async_engine(
    DATABASE_URL,
    pool_size=DB_POOL_SIZE,
    max_overflow=0,
    echo=True if os.getenv("NODE_ENV") == "development" else False
)

# Create sync engine for migrations
sync_engine = create_engine(SYNC_DATABASE_URL)

# Create session makers
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

SessionLocal = sessionmaker(bind=sync_engine)

# Base class for models
Base = declarative_base()

# Database Models
class User(Base):
    """User model for chat sessions"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String(255), unique=True, index=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    last_active = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    messages = relationship("ChatMessage", back_populates="user", cascade="all, delete-orphan")

class Document(Base):
    """Document model for uploaded PDFs"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    content_hash = Column(String(64), unique=True, index=True)
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")

class DocumentChunk(Base):
    """Document chunks for vector search"""
    __tablename__ = "document_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    chunk_text = Column(Text, nullable=False)
    chunk_index = Column(Integer, nullable=False)
    page_number = Column(Integer)
    vector_id = Column(String(100))  # Reference to vector in FAISS index
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")

class ChatMessage(Base):
    """Chat message model"""
    __tablename__ = "chat_messages"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    message = Column(Text, nullable=False)
    response = Column(Text)
    message_type = Column(String(50), default="user")  # user, assistant, system
    context_documents = Column(Text)  # JSON string of relevant document IDs
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="messages")

# Database utility functions
async def get_async_session():
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

def get_sync_session():
    """Get sync database session"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

async def init_db():
    """Initialize database tables"""
    try:
        async with async_engine.begin() as conn:
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
        print("✅ Database tables created successfully")
    except Exception as e:
        print(f"❌ Error creating database tables: {e}")
        raise

async def close_db():
    """Close database connections"""
    try:
        await async_engine.dispose()
        print("✅ Database connections closed")
    except Exception as e:
        print(f"❌ Error closing database connections: {e}")

# Database helper functions
async def get_or_create_user(session_id: str) -> User:
    """Get or create user by session ID"""
    async with AsyncSessionLocal() as session:
        # Try to find existing user
        result = await session.execute(
            "SELECT * FROM users WHERE session_id = :session_id",
            {"session_id": session_id}
        )
        user = result.fetchone()
        
        if not user:
            # Create new user
            user = User(session_id=session_id)
            session.add(user)
            await session.commit()
            await session.refresh(user)
        
        return user

async def save_chat_message(user_id: int, message: str, response: str = None, 
                          message_type: str = "user", context_documents: str = None):
    """Save chat message to database"""
    async with AsyncSessionLocal() as session:
        chat_message = ChatMessage(
            user_id=user_id,
            message=message,
            response=response,
            message_type=message_type,
            context_documents=context_documents
        )
        session.add(chat_message)
        await session.commit()
        await session.refresh(chat_message)
        return chat_message

async def get_chat_history(user_id: int, limit: int = 50) -> List[ChatMessage]:
    """Get chat history for a user"""
    async with AsyncSessionLocal() as session:
        result = await session.execute(
            "SELECT * FROM chat_messages WHERE user_id = :user_id ORDER BY created_at DESC LIMIT :limit",
            {"user_id": user_id, "limit": limit}
        )
        return result.fetchall()

if __name__ == "__main__":
    # Test database connection
    async def test_connection():
        await init_db()
        print("Database connection test successful!")
    
    asyncio.run(test_connection())
