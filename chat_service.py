"""
Chat Service - Core business logic for chatbot functionality
"""
import os
import json
import uuid
import redis
import asyncio
from datetime import datetime
from typing import List, Dict, Optional
import google.generativeai as genai
from dotenv import load_dotenv

from database import (
    create_chat, chat_exists, save_message, get_chat_history, 
    update_chat_timestamp
)

load_dotenv()

class ChatService:
    """Main chat service handling all chat operations"""
    
    def __init__(self):
        # Initialize Redis for caching
        self.redis_client = redis.Redis.from_url(
            os.getenv("REDIS_URL", "redis://localhost:6379"),
            decode_responses=True
        )
        
        # Initialize Gemini AI
        genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
        self.model = genai.GenerativeModel(
            model_name=os.getenv("GEMINI_MODEL", "gemini-1.5-flash"),
            system_instruction=self._get_system_prompt()
        )
        
        # Configuration
        self.cache_ttl = 3600  # 1 hour
        self.max_history_messages = 10
        self.max_content_length = 4096
        
        print("✅ ChatService initialized")
    
    def _get_system_prompt(self) -> str:
        """Get Indonesian language system prompt"""
        return """
        Anda adalah asisten AI yang membantu di kantor DPMPTSP (Dinas Penanaman Modal dan Pelayanan Terpadu Satu Pintu).
        
        Tugas Anda:
        1. Membantu menjawab pertanyaan tentang layanan perizinan dan investasi
        2. Memberikan informasi yang akurat berdasarkan dokumen yang tersedia
        3. Berbicara dalam bahasa Indonesia yang sopan dan profesional
        4. Jika informasi tidak tersedia dalam dokumen, berikan saran umum yang membantu
        
        Gaya komunikasi:
        - Gunakan bahasa Indonesia formal namun ramah
        - Berikan jawaban yang jelas dan terstruktur
        - Sertakan referensi dokumen jika relevan
        - Tawarkan bantuan lebih lanjut jika diperlukan
        
        Jika Anda tidak memiliki informasi yang cukup, sarankan untuk:
        - Menghubungi kantor DPMPTSP langsung
        - Mengunjungi website resmi
        - Datang langsung ke kantor untuk konsultasi
        """
    
    async def create_new_chat(self, client_provided_id: Optional[str] = None) -> str:
        """Create new chat session"""
        try:
            # Validate client provided ID if given
            if client_provided_id:
                try:
                    uuid.UUID(client_provided_id)
                    # Check if chat already exists
                    if await chat_exists(client_provided_id):
                        return client_provided_id
                    else:
                        # Create chat with provided ID
                        chat_id = client_provided_id
                except ValueError:
                    # Invalid UUID, create new one
                    chat_id = await create_chat()
            else:
                # Create new chat
                chat_id = await create_chat()
            
            # Cache welcome message
            welcome_message = self._get_welcome_message()
            await self._cache_chat_history(chat_id, [welcome_message])
            
            return chat_id
            
        except Exception as e:
            print(f"Error creating chat: {e}")
            raise
    
    def _get_welcome_message(self) -> dict:
        """Get welcome message for new chats"""
        return {
            "id": str(uuid.uuid4()),
            "role": "assistant",
            "content": "Selamat datang di layanan chatbot DPMPTSP! Saya siap membantu Anda dengan informasi tentang perizinan dan investasi. Silakan ajukan pertanyaan Anda.",
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def get_chat_history_cached(self, chat_id: str) -> List[dict]:
        """Get chat history with Redis caching"""
        try:
            # Try to get from cache first
            cached_history = await self._get_cached_chat_history(chat_id)
            if cached_history:
                return cached_history
            
            # Get from database
            history = await get_chat_history(chat_id, self.max_history_messages)
            
            # Cache the result
            await self._cache_chat_history(chat_id, history)
            
            return history
            
        except Exception as e:
            print(f"Error getting chat history: {e}")
            return []
    
    async def send_message(self, chat_id: str, content: str) -> dict:
        """Process user message and generate AI response"""
        try:
            # Validate chat exists
            if not await chat_exists(chat_id):
                raise ValueError("Chat not found")
            
            # Truncate content if too long
            content = content[:self.max_content_length]
            
            # Save user message
            user_message = await save_message(chat_id, content, "user")
            
            # Get chat history for context
            history = await self.get_chat_history_cached(chat_id)
            
            # Generate AI response
            ai_response = await self._generate_ai_response(content, history)
            
            # Save AI response
            assistant_message = await save_message(chat_id, ai_response, "assistant")
            
            # Update chat timestamp
            await update_chat_timestamp(chat_id)
            
            # Update cache
            updated_history = history + [user_message, assistant_message]
            await self._cache_chat_history(chat_id, updated_history[-self.max_history_messages:])
            
            return {
                "user_message": user_message,
                "assistant_message": assistant_message
            }
            
        except Exception as e:
            print(f"Error processing message: {e}")
            raise
    
    async def _generate_ai_response(self, user_message: str, history: List[dict]) -> str:
        """Generate AI response using Gemini"""
        try:
            # Build conversation context
            conversation = []
            for msg in history[-5:]:  # Use last 5 messages for context
                if msg["role"] in ["user", "assistant"]:
                    conversation.append(f"{msg['role']}: {msg['content']}")
            
            # Add current message
            conversation.append(f"user: {user_message}")
            
            # Create prompt
            prompt = "\n".join(conversation)
            
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=1000,
                    top_p=0.8,
                    top_k=40
                )
            )
            
            return response.text.strip()
            
        except Exception as e:
            print(f"Error generating AI response: {e}")
            return "Maaf, terjadi kesalahan dalam memproses permintaan Anda. Silakan coba lagi atau hubungi kantor DPMPTSP untuk bantuan langsung."
    
    async def reset_chat(self, old_chat_id: str, new_chat_id: Optional[str] = None) -> dict:
        """Reset chat session"""
        try:
            # Create new chat
            new_id = await self.create_new_chat(new_chat_id)
            
            # Clear old chat cache
            await self._clear_chat_cache(old_chat_id)
            
            return {
                "old_chat_id": old_chat_id,
                "new_chat_id": new_id
            }
            
        except Exception as e:
            print(f"Error resetting chat: {e}")
            raise
    
    # Redis caching methods
    async def _cache_chat_history(self, chat_id: str, history: List[dict]):
        """Cache chat history in Redis"""
        try:
            cache_key = f"chat_history:{chat_id}"
            await asyncio.to_thread(
                self.redis_client.setex,
                cache_key,
                self.cache_ttl,
                json.dumps(history, default=str)
            )
        except Exception as e:
            print(f"Error caching chat history: {e}")
    
    async def _get_cached_chat_history(self, chat_id: str) -> Optional[List[dict]]:
        """Get cached chat history from Redis"""
        try:
            cache_key = f"chat_history:{chat_id}"
            cached_data = await asyncio.to_thread(
                self.redis_client.get,
                cache_key
            )
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            print(f"Error getting cached chat history: {e}")
            return None
    
    async def _clear_chat_cache(self, chat_id: str):
        """Clear chat cache"""
        try:
            cache_key = f"chat_history:{chat_id}"
            await asyncio.to_thread(self.redis_client.delete, cache_key)
        except Exception as e:
            print(f"Error clearing chat cache: {e}")
    
    def validate_chat_id(self, chat_id: str) -> bool:
        """Validate chat ID format"""
        try:
            uuid.UUID(chat_id)
            return True
        except (ValueError, TypeError):
            return False
