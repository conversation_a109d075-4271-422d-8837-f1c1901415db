"""
FastAPI Chatbot Backend with Gemini AI, PostgreSQL, and Vector Search
"""
import os
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import socketio
from dotenv import load_dotenv

from database import init_db, close_db
from chat_service import ChatService
from check_models import check_database_connection, check_gemini_api, check_vector_db

# Load environment variables
load_dotenv()

# Create data directories if they don't exist
os.makedirs("data/docs", exist_ok=True)
os.makedirs("vector_db", exist_ok=True)

# Initialize Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins=os.getenv('FRONTEND_URL', 'http://localhost:3001')
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 Starting Chatbot Backend...")
    
    # Initialize database
    await init_db()
    print("✅ Database initialized")
    
    # Check all systems
    await check_database_connection()
    await check_gemini_api()
    await check_vector_db()
    
    # Initialize chat service
    app.state.chat_service = ChatService()
    print("✅ Chat service initialized")
    
    print("🎉 Chatbot Backend is ready!")
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Chatbot Backend...")
    await close_db()
    print("✅ Database connections closed")

# Create FastAPI app
app = FastAPI(
    title="Chatbot Backend",
    description="AI-powered chatbot with PDF processing and vector search",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv('FRONTEND_URL', 'http://localhost:3001')],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount Socket.IO
socket_app = socketio.ASGIApp(sio, app)

# Socket.IO event handlers
@sio.event
async def connect(sid, environ):
    """Handle client connection"""
    print(f"Client {sid} connected")
    await sio.emit('connected', {'message': 'Connected to chatbot'}, room=sid)

@sio.event
async def disconnect(sid):
    """Handle client disconnection"""
    print(f"Client {sid} disconnected")

@sio.event
async def send_message(sid, data):
    """Handle incoming chat messages"""
    try:
        message = data.get('message', '')
        if not message:
            await sio.emit('error', {'error': 'Message is required'}, room=sid)
            return
        
        # Get chat service
        chat_service = app.state.chat_service
        
        # Process message and get AI response
        response = await chat_service.process_message(message, sid)
        
        # Send response back to client
        await sio.emit('message_response', {
            'message': response,
            'timestamp': response.get('timestamp')
        }, room=sid)
        
    except Exception as e:
        print(f"Error processing message: {e}")
        await sio.emit('error', {'error': 'Failed to process message'}, room=sid)

# REST API Routes
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Chatbot Backend is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Check database
        db_status = await check_database_connection()
        
        # Check Gemini API
        gemini_status = await check_gemini_api()
        
        # Check vector database
        vector_status = await check_vector_db()
        
        return {
            "status": "healthy",
            "database": db_status,
            "gemini_api": gemini_status,
            "vector_db": vector_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")

@app.post("/upload-document")
async def upload_document():
    """Upload and process PDF documents"""
    # This will be implemented in chat_service
    return {"message": "Document upload endpoint - to be implemented"}

@app.get("/documents")
async def list_documents():
    """List uploaded documents"""
    # This will be implemented in chat_service
    return {"documents": []}

if __name__ == "__main__":
    port = int(os.getenv("PORT", 3000))
    uvicorn.run(
        "main:socket_app",
        host="0.0.0.0",
        port=port,
        reload=True if os.getenv("NODE_ENV") == "development" else False
    )
